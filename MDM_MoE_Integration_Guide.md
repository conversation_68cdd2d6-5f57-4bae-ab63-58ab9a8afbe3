# MDM 中使用 MoE (Mixture of Experts) 集成指南

## 概述

本指南详细说明如何在 MDM (Motion Diffusion Model) 中集成和使用 MoE 架构，实现更高效的动作生成模型。

## 🎯 主要改动

### 1. MDM 类的修改

#### 新增参数
```python
def __init__(self, ..., 
             # MoE parameters
             use_moe=False,           # 是否启用 MoE
             num_experts=8,           # 专家数量
             top_k=2,                 # 每个 token 激活的专家数
             load_balance_weight=0.01, # 负载均衡损失权重
             **kwargs):
```

#### 核心修改
1. **架构选择**: 在 `arch='trans_dec'` 时支持 MoE
2. **负载均衡**: 自动处理 MoE 的负载均衡损失
3. **损失组合**: 提供方法组合主损失和负载均衡损失

### 2. 新增的关键方法

```python
# 获取负载均衡损失
load_loss = model.get_load_balancing_loss()

# 获取组合后的总损失
total_loss = model.get_total_loss(main_loss)
```

## 🚀 使用方法

### 基本使用

```python
from closd.diffusion_planner.model.mdm import MDM

# 创建带 MoE 的 MDM 模型
model = MDM(
    modeltype='mdm',
    njoints=22,
    nfeats=6,
    arch='trans_dec',        # 必须使用 transformer decoder
    cond_mode='text',
    
    # MoE 配置
    use_moe=True,           # 启用 MoE
    num_experts=8,          # 8 个专家
    top_k=2,                # 每个 token 激活 2 个专家
    load_balance_weight=0.01, # 负载均衡权重
    
    # 其他标准参数
    latent_dim=512,
    ff_size=1024,
    num_layers=8,
    num_heads=8,
    dropout=0.1
)
```

### 训练循环

```python
import torch.nn as nn

# 创建优化器
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

# 训练步骤
def training_step(model, x, timesteps, y, target):
    # 前向传播
    output = model(x, timesteps, y)
    
    # 计算主损失
    main_loss = nn.MSELoss()(output, target)
    
    # 获取总损失（包含负载均衡）
    total_loss = model.get_total_loss(main_loss)
    
    # 反向传播
    optimizer.zero_grad()
    total_loss.backward()
    optimizer.step()
    
    # 监控损失
    load_loss = model.get_load_balancing_loss()
    print(f"Main: {main_loss:.6f}, Load: {load_loss:.6f}, Total: {total_loss:.6f}")
    
    return total_loss
```

## 📊 性能特点

### 测试结果

根据我们的测试：

```
标准 MDM 参数: 154,771,333
MoE MDM 参数:  162,141,093
参数比例: 1.05x (仅增加 5%)
```

### 关键优势

1. **参数效率**: 虽然总参数略有增加，但每次前向传播只使用部分专家
2. **专业化能力**: 不同专家可以学习处理不同类型的动作模式
3. **负载均衡**: 自动确保专家使用的均匀分布

### 负载均衡监控

```python
# 监控专家利用率
model.eval()
total_load_loss = 0.0
num_passes = 10

for i in range(num_passes):
    with torch.no_grad():
        _ = model(x, timesteps, y)
        total_load_loss += model.get_load_balancing_loss()

avg_load_loss = total_load_loss / num_passes
print(f"平均负载均衡损失: {avg_load_loss:.6f}")
# 数值越低表示专家使用越均匀
```

## ⚙️ 配置建议

### 超参数选择

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `num_experts` | 4-16 | 专家数量，更多专家需要更大数据集 |
| `top_k` | 1-2 | 激活专家数，平衡专业化和稳定性 |
| `load_balance_weight` | 0.01-0.1 | 负载均衡权重，根据任务调整 |

### 不同场景的配置

#### 小规模实验
```python
use_moe=True,
num_experts=4,
top_k=2,
load_balance_weight=0.01
```

#### 大规模训练
```python
use_moe=True,
num_experts=16,
top_k=2,
load_balance_weight=0.05
```

#### 推理优化
```python
use_moe=True,
num_experts=8,
top_k=1,  # 减少计算量
load_balance_weight=0.01
```

## 🔧 实现细节

### 架构兼容性

- ✅ **支持**: `arch='trans_dec'` + `use_moe=True`
- ❌ **不支持**: `arch='trans_enc'` 或 `arch='gru'` 与 MoE 组合

### 条件模式兼容性

- ✅ **文本条件**: `cond_mode='text'` (CLIP/BERT)
- ✅ **动作条件**: `cond_mode='action'`
- ✅ **组合条件**: `cond_mode='text_action'`

### 数据格式要求

输入数据格式保持不变：
```python
x: [batch_size, njoints, nfeats, nframes]  # 动作数据
timesteps: [batch_size]                     # 扩散时间步
y: dict                                     # 条件信息
```

## 🐛 常见问题

### Q1: 如何判断 MoE 是否正常工作？
```python
# 检查负载均衡损失
load_loss = model.get_load_balancing_loss()
if load_loss > 0:
    print("MoE 正常工作")
else:
    print("可能未启用 MoE 或出现问题")
```

### Q2: 负载均衡损失过高怎么办？
- 减少 `load_balance_weight`
- 增加 `num_experts`
- 检查数据分布是否均匀

### Q3: 训练不稳定怎么办？
- 降低学习率
- 使用梯度裁剪
- 逐渐增加 `load_balance_weight`

## 📈 监控和调试

### 训练监控
```python
# 记录关键指标
metrics = {
    'main_loss': main_loss.item(),
    'load_loss': model.get_load_balancing_loss(),
    'total_loss': total_loss.item(),
    'load_ratio': model.get_load_balancing_loss() / main_loss.item()
}
```

### 专家利用率分析
```python
# 分析专家使用情况
def analyze_expert_usage(model, dataloader, num_batches=10):
    model.eval()
    total_load_loss = 0.0
    
    with torch.no_grad():
        for i, (x, timesteps, y) in enumerate(dataloader):
            if i >= num_batches:
                break
            _ = model(x, timesteps, y)
            total_load_loss += model.get_load_balancing_loss()
    
    avg_load_loss = total_load_loss / num_batches
    return avg_load_loss
```

## 🎯 最佳实践

1. **渐进式训练**: 先用标准 MDM 预训练，再切换到 MoE
2. **负载均衡调优**: 监控专家利用率，调整权重
3. **内存管理**: MoE 模型内存需求更高，注意批次大小
4. **推理优化**: 推理时可以考虑减少 `top_k` 值

## 📝 总结

MoE 集成为 MDM 提供了：
- 🚀 **更强的表达能力**: 专家专业化处理不同动作模式
- ⚡ **计算效率**: 每次只激活部分专家
- 🎯 **灵活配置**: 可根据需求调整专家数量和路由策略
- 📊 **自动负载均衡**: 确保专家使用的均匀分布

通过合理配置和监控，MoE 可以显著提升 MDM 在复杂动作生成任务上的性能。
