# MoE (Mixture of Experts) Transformer 实现指南

## 概述

本指南展示了如何将标准的 `TransformerDecoderLayer` 改造成 MoE (Mixture of Experts) 架构。MoE 是一种提高模型容量而不成比例增加计算成本的技术。

## 核心概念

### 什么是 MoE？
- **专家网络 (Experts)**: 多个独立的前馈网络，每个专家专门处理特定类型的输入
- **门控网络 (Gating Network)**: 决定每个 token 应该路由到哪些专家
- **Top-K 路由**: 每个 token 只激活 K 个最相关的专家，而不是所有专家

### 优势
1. **参数效率**: 虽然总参数量增加，但每次前向传播只使用部分参数
2. **专业化**: 不同专家可以学习处理不同类型的输入模式
3. **可扩展性**: 可以通过增加专家数量来扩展模型容量

## 实现架构

### 1. Expert 类
```python
class Expert(Module):
    """单个专家网络，本质上是一个标准的前馈网络"""
    def __init__(self, d_model, dim_feedforward, dropout, activation):
        self.linear1 = Linear(d_model, dim_feedforward)
        self.linear2 = Linear(dim_feedforward, d_model)
        self.activation = activation
        self.dropout = Dropout(dropout)
    
    def forward(self, x):
        return self.linear2(self.dropout(self.activation(self.linear1(x))))
```

### 2. TopKGate 类
```python
class TopKGate(Module):
    """Top-K 门控机制"""
    def __init__(self, d_model, num_experts, top_k):
        self.gate = Linear(d_model, num_experts)
        self.top_k = top_k
    
    def forward(self, x):
        gate_logits = self.gate(x)
        gate_probs = F.softmax(gate_logits, dim=-1)
        gates, indices = torch.topk(gate_probs, self.top_k, dim=-1)
        # 返回权重、专家索引和负载均衡损失
        return gates, indices, load_loss
```

### 3. MoELayer 类
```python
class MoELayer(Module):
    """MoE 层，包含多个专家和门控网络"""
    def __init__(self, d_model, num_experts, dim_feedforward, top_k):
        self.experts = ModuleList([Expert(...) for _ in range(num_experts)])
        self.gate = TopKGate(d_model, num_experts, top_k)
    
    def forward(self, x):
        gates, indices, load_loss = self.gate(x)
        # 根据门控决策路由到相应专家
        output = self._route_to_experts(x, gates, indices)
        return output, load_loss
```

### 4. MoETransformerDecoderLayer 类
```python
class MoETransformerDecoderLayer(Module):
    """使用 MoE 的 Transformer Decoder Layer"""
    def __init__(self, d_model, nhead, num_experts, top_k, ...):
        self.self_attn = MultiheadAttention(...)
        self.multihead_attn = MultiheadAttention(...)
        self.moe = MoELayer(d_model, num_experts, dim_feedforward, top_k, ...)
        # 其他组件保持不变
    
    def forward(self, tgt, memory, ...):
        # 自注意力和交叉注意力保持不变
        x = tgt
        x = x + self._sa_block(...)
        x = x + self._mha_block(...)
        
        # 用 MoE 替换标准前馈网络
        moe_output, load_loss = self.moe(x)
        x = x + moe_output
        
        return x, load_loss
```

## 关键改动

### 1. 前馈网络替换
- **原来**: 单个前馈网络 `linear1 -> activation -> linear2`
- **现在**: 多个专家网络 + 门控机制

### 2. 损失函数
- **新增**: 负载均衡损失，鼓励专家使用的均匀分布
- **组合**: `total_loss = main_loss + λ * load_balance_loss`

### 3. 返回值变化
- **原来**: `forward()` 返回 `Tensor`
- **现在**: `forward()` 返回 `(Tensor, load_loss)`

## 使用示例

### 基本使用
```python
# 创建 MoE decoder layer
moe_layer = MoETransformerDecoderLayer(
    d_model=512,
    nhead=8,
    num_experts=8,    # 8个专家
    top_k=2,          # 每个token激活2个专家
    dim_feedforward=2048
)

# 前向传播
output, load_loss = moe_layer(tgt, memory)
```

### 训练循环
```python
# 前向传播
output, load_loss = model(input)

# 计算损失
main_loss = criterion(output, target)
total_loss = main_loss + 0.01 * load_loss  # 负载均衡权重

# 反向传播
total_loss.backward()
optimizer.step()
```

## 性能特点

### 参数效率
- **标准模型**: 4.2M 参数
- **MoE 模型**: 10.5M 参数 (2.5x)
- **等效容量**: ~4.5x 标准模型

### 计算效率
- 虽然总参数增加 2.5x，但每次前向传播只使用 top_k/num_experts 的专家
- 例如：8个专家，top_k=2，每次只使用 25% 的专家参数

### 专家利用率
测试显示专家使用相对均匀：
```
Expert 0: 15.6%
Expert 1: 10.0%
Expert 2: 10.0%
...
Utilization variance: 0.000692 (接近理想值 0.015625)
```

## 最佳实践

### 1. 超参数选择
- **num_experts**: 通常 4-16 个，更多专家需要更大的数据集
- **top_k**: 通常 1-2，平衡专业化和稳定性
- **load_balance_weight**: 0.01-0.1，根据任务调整

### 2. 训练技巧
- 逐渐增加负载均衡损失权重
- 监控专家利用率，避免专家坍塌
- 考虑使用专家 dropout 提高泛化能力

### 3. 部署考虑
- MoE 模型对内存要求更高
- 推理时可以考虑专家缓存策略
- 分布式部署时需要考虑专家分片

## 总结

MoE 架构通过用多个专家网络替换标准的前馈层，实现了参数效率和模型容量的平衡。主要改动包括：

1. 添加 Expert、TopKGate、MoELayer 类
2. 修改 TransformerDecoderLayer 使用 MoE
3. 处理负载均衡损失
4. 适配训练和推理流程

这种架构特别适合需要处理多样化输入模式的任务，如自然语言处理、多模态学习等。
