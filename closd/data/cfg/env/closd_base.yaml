defaults:
  - im_single_prim

motion_file: data/sample_data/amass_isaac_standing_upright_slim.pkl
models: output/HumanoidIm/PhcSinglePrimForever/Humanoid.pth  # FIXME: this is a workaround. model loading should not be called at all
task: HumanoidImMCPMDM  # closed loop without any task
obs_v: 7 
num_envs: 1
disable_adaptnet: True
episode_length: 999999  # no reset
enable_transitions: True  # state machine transitions according to done signal

dip:
  prompt: 'A person is walking.'
  planning_horizon: 40
  cfg_param: 7.5
  traj_guidance: False
  debug_hml: False
  model_path: ./closd/diffusion_planner/save/DiP_multi-target_10steps_context20_predict40/model000300000.pt

# No early reset when the following joints touch the ground:
contact_bodies: ['L_Knee', '<PERSON>_Ankle', 'L_Toe', 'R_Knee', '<PERSON>_<PERSON>kle', 'R_Toe', 'Torso', 'Chest', 'L_Thorax', '<PERSON>_Shoulder', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON>']
# contact_bodies: ['<PERSON><PERSON><PERSON>', '<PERSON>_Hip', 'L_Knee', 'L_Ankle', 'L_Toe', 'R_Hip', 'R_Knee', 'R_Ankle', 'R_Toe', 'Torso', 'Chest', 'L_Thorax', 'L_Shoulder', 'L_Elbow', 'L_Wrist', 'L_Hand', 'R_Thorax', 'R_Shoulder', 'R_Elbow', 'R_Wrist', 'R_Hand']

############################## Debug ##############################
# mujoco_joint_names = ['Pelvis', 'L_Hip', 'L_Knee', 'L_Ankle', 'L_Toe', 'R_Hip', 'R_Knee', 'R_Ankle', 'R_Toe', 'Torso', 'Spine', 'Chest', 'Neck', 'Head', 'L_Thorax', 'L_Shoulder', 'L_Elbow', 'L_Wrist', 'L_Hand', 'R_Thorax', 'R_Shoulder', 'R_Elbow', 'R_Wrist', 'R_Hand']
############################## Debug ##############################

# for training
shape_resampling_interval: 100000000000000000
