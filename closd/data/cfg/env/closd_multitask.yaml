defaults:
  - closd_base

task: CLoSDMultiTask
episode_length: 400
getup_prob: 1.
task_filter: 'none'  # [bench, strike, reach]


# No early reset when the following joints touch the box:
strikeBodyNames: ['<PERSON><PERSON><PERSON>', '<PERSON>_<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Chest', 'Neck', 'Head', '<PERSON>_<PERSON><PERSON>', '<PERSON>_Should<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_Should<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON>']

############################## Debug ##############################
# mujoco_joint_names = ['<PERSON><PERSON><PERSON>', '<PERSON>_<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON><PERSON>', 'Spin<PERSON>', 'Chest', '<PERSON>', '<PERSON>', '<PERSON>_<PERSON><PERSON>', '<PERSON>_Shoulder', '<PERSON>_<PERSON>bow', '<PERSON>_Wrist', 'L_Hand', 'R_<PERSON>ax', 'R_Shoulder', 'R_<PERSON>bow', 'R_Wrist', 'R_Hand']
############################## Debug ##############################