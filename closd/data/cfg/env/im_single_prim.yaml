defaults:
  - dip: dip_defaults

# if given, will override the device setting in gym. 
task: HumanoidIm
imitator: HumanoidIm
project_name: "CLoSD"
notes:  ""
motion_file: data/amass/amass_train_take6_upright.pkl
num_envs: 3072
env_spacing: 5
episode_length: 300
is_flag_run: False
enable_debug_vis: False
show_markers: True

fut_tracks: False
self_obs_v: 1
obs_v: 7
auto_pmcp: False
auto_pmcp_soft: True

cycle_motion: False
cycle_motion_xp: False
hard_negative: False
min_length: 5

kp_scale: 1
power_reward: True

shape_resampling_interval: 500

control_mode: "isaac_pd"
power_scale: 1.0
controlFrequencyInv: 2 # 30 Hz
stateInit: "Random"  
hybridInitProb: 0.5
numAMPObsSteps: 10

local_root_obs: True
root_height_obs: True
key_bodies: ["<PERSON>_<PERSON><PERSON>", "<PERSON>_<PERSON>kle", "<PERSON>_<PERSON><PERSON>",  "L_Wrist"]
contact_bodies: ["<PERSON>_<PERSON><PERSON>", "<PERSON>_<PERSON><PERSON>", "<PERSON>_<PERSON><PERSON>", "<PERSON>_Toe"]
reset_bodies: ['<PERSON>el<PERSON>', 'L_Hip', 'L_Knee', 'R_Hip', 'R_Knee', 'Torso', 'Spine', 'Chest', 'Neck', 'Head', 'L_Thorax', 'L_Shoulder', 'L_Elbow', 'L_Wrist', 'L_Hand', 'R_Thorax', 'R_Shoulder', 'R_Elbow', 'R_Wrist', 'R_Hand']
strikeBodyNames: ['L_Hand']  # placeholder for strike task only
terminationHeight: 0.15
enableEarlyTermination: True
terminationDistance: 0.25

strike_asset_density: 200.
bench_asset_density: 1000.

### Fut config
numTrajSamples: 3
trajSampleTimestepInv: 3
enableTaskObs: True

# AdaptNet
models: []


# For AdaptNet
aux_coefficient: 0.0
disable_adaptnet: True  # use for bypassing adaptnet and use only the task

# For goal reaching only
tarSpeed: 1.0
tarChangeStepsMin: 50
tarChangeStepsMax: 100
tarDistMax: 1.0
tarHeightMin: 0.9
tarHeightMax: 0.95
reachBodyName: 'Pelvis'


plane:
  staticFriction: 1.0
  dynamicFriction: 1.0
  restitution: 0.0




support_phc_markers: False
done_dist: 0.3

