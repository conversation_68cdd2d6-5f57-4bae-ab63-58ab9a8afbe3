sim_device: "cuda:0"
pipeline: "gpu"
graphics_device_id: 0
subscenes: 0 # Number of PhysX subscenes to simulate in parallel
slices: 0 # Number of client threads that process env slices
use_flex: False

substeps: 2
physx:
  num_threads: 4
  solver_type: 1  # 0: pgs, 1: tgs
  num_position_iterations: 4
  num_velocity_iterations: 0
  contact_offset: 0.02
  rest_offset: 0.0
  bounce_threshold_velocity: 0.2
  max_depenetration_velocity: 10.0
  default_buffer_size_multiplier: 10.0

flex:
  num_inner_iterations: 10
  warm_start: 0.25
