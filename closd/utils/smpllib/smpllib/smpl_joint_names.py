import os
import sys
import time
import argparse
import torch
import pdb
import os.path as osp



SMPL_BONE_ORDER_NAMES = [
    "<PERSON>elvis",
    "L_Hip",
    "R_Hip",
    "Torso",
    "L_Knee",
    "<PERSON>_K<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>_<PERSON>kle",
    "<PERSON>_<PERSON>kle",
    "Chest",
    "L_Toe",
    "R_Toe",
    "Neck",
    "L_Thorax",
    "R_Thorax",
    "Head",
    "L_Shoulder",
    "R_Shoulder",
    "L_Elbow",
    "R_Elbow",
    "L_Wrist",
    "R_Wrist",
    "L_Hand",
    "R_Hand",
]

SMPLH_BONE_ORDER_NAMES = [
    "<PERSON><PERSON>vis",
    "L_Hip",
    "R_Hip",
    "Torso",
    "L_Knee",
    "R_Knee",
    "Spine",
    "L_Ankle",
    "R_Ankle",
    "Chest",
    "L_Toe",
    "R_Toe",
    "Neck",
    "L_Thorax",
    "R_Thorax",
    "<PERSON>",
    "<PERSON>_Shoulder",
    "<PERSON>_Should<PERSON>",
    "<PERSON>_Elbow",
    "<PERSON>_<PERSON><PERSON>",
    "<PERSON>_W<PERSON>",
    "<PERSON>_Wrist",
    "L_Index1",
    "L_Index2",
    "L_Index3",
    "L_Middle1",
    "L_Middle2",
    "L_Middle3",
    "L_Pinky1",
    "L_Pinky2",
    "L_Pinky3",
    "L_Ring1",
    "L_Ring2",
    "L_Ring3",
    "L_Thumb1",
    "L_Thumb2",
    "L_Thumb3",
    "R_Index1",
    "R_Index2",
    "R_Index3",
    "R_Middle1",
    "R_Middle2",
    "R_Middle3",
    "R_Pinky1",
    "R_Pinky2",
    "R_Pinky3",
    "R_Ring1",
    "R_Ring2",
    "R_Ring3",
    "R_Thumb1",
    "R_Thumb2",
    "R_Thumb3",
]

SMPLX_BONE_ORDER_NAMES = [
    "Pelvis",
    "L_Hip",
    "R_Hip",
    "Torso",
    "L_Knee",
    "R_Knee",
    "Spine",
    "L_Ankle",
    "R_Ankle",
    "Chest",
    "L_Toe",
    "R_Toe",
    "Neck",
    "L_Thorax",
    "R_Thorax",
    "Head",
    "L_Shoulder",
    "R_Shoulder",
    "L_Elbow",
    "R_Elbow",
    "L_Wrist",
    "R_Wrist",
    "Jaw",
    "L_Eye_Smplhf",
    "R_Eye_Smplhf",
    "L_Index1",
    "L_Index2",
    "L_Index3",
    "L_Middle1",
    "L_Middle2",
    "L_Middle3",
    "L_Pinky1",
    "L_Pinky2",
    "L_Pinky3",
    "L_Ring1",
    "L_Ring2",
    "L_Ring3",
    "L_Thumb1",
    "L_Thumb2",
    "L_Thumb3",
    "R_Index1",
    "R_Index2",
    "R_Index3",
    "R_Middle1",
    "R_Middle2",
    "R_Middle3",
    "R_Pinky1",
    "R_Pinky2",
    "R_Pinky3",
    "R_Ring1",
    "R_Ring2",
    "R_Ring3",
    "R_Thumb1",
    "R_Thumb2",
    "R_Thumb3",
    "Nose",
    "R_Eye",
    "L_Eye",
    "R_Ear",
    "R_Ear",
    "L_Big_Toe",
    "L_Small_Toe",
    "L_Heel",
    "R_Big_Toe",
    "R_Small_Toe",
    "R_heel",
    "L_thumb",
    "L_index",
    "L_middle",
    "L_ring",
    "L_Pinky",
    "R_thumb",
    "R_index",
    "R_middle",
    "R_ring",
    "R_Pinky",
    "R_Eye_Bow1",
    "R_Eye_Bow2",
    "R_Eye_Bow3",
    "R_Eye_Bow4",
    "R_Eye_Bow5",
    "L_Eye_Bow5",
    "L_Eye_Bow4",
    "L_Eye_Bow3",
    "L_Eye_Bow2",
    "L_Eye_Bow1",
    "Nose1",
    "Nose2",
    "Nose3",
    "Nose4",
    "R_Nose_2",
    "R_Nose_1",
    "Nose_middle",
    "L_Nose_1",
    "L_Nose_2",
    "R_eye1",
    "R_eye2",
    "R_eye3",
    "R_eye4",
    "R_eye5",
    "R_eye6",
    "L_eye4",
    "L_eye3",
    "L_eye2",
    "L_eye1",
    "L_eye6",
    "L_eye5",
    "R_Mouth_1",
    "R_Mouth_2",
    "R_Mouth_3",
    "mouth_top",
    "L_Mouth_3",
    "L_Mouth_2",
    "L_Mouth_1",
    "L_Mouth_5",  # 59 in OpenPose output
    "L_Mouth_4",  # 58 in OpenPose output
    "Mouth_Bottom",
    "R_Mouth_4",
    "R_Mouth_5",
    "R_lip_1",
    "R_lip_2",
    "Lip_Top",
    "L_lip_2",
    "L_lip_1",
    "L_lip_3",
    "Lip_Bottom",
    "R_lip_3",
]

SMPL_MUJOCO_NAMES = ['Pelvis', 'L_Hip', 'L_Knee', 'L_Ankle', 'L_Toe', 'R_Hip', 'R_Knee', 'R_Ankle', 'R_Toe', 'Torso', 'Spine', 'Chest', 'Neck', 'Head', 'L_Thorax', 'L_Shoulder', 'L_Elbow', 'L_Wrist', 'L_Hand', 'R_Thorax', 'R_Shoulder', 'R_Elbow', 'R_Wrist', 'R_Hand']
SMPLH_MUJOCO_NAMES = ['Pelvis', 'L_Hip', 'L_Knee', 'L_Ankle', 'L_Toe', 'R_Hip', 'R_Knee', 'R_Ankle', 'R_Toe', 'Torso', 'Spine', 'Chest', 'Neck', 'Head', 'L_Thorax', 'L_Shoulder', 'L_Elbow', 'L_Wrist', 'L_Index1', 'L_Index2', 'L_Index3', 'L_Middle1', 'L_Middle2', 'L_Middle3', 'L_Pinky1', 'L_Pinky2', 'L_Pinky3', 'L_Ring1', 'L_Ring2', 'L_Ring3', 'L_Thumb1', 'L_Thumb2', 'L_Thumb3', 'R_Thorax', 'R_Shoulder', 'R_Elbow', 'R_Wrist', 'R_Index1', 'R_Index2', 'R_Index3', 'R_Middle1', 'R_Middle2', 'R_Middle3', 'R_Pinky1', 'R_Pinky2', 'R_Pinky3', 'R_Ring1', 'R_Ring2', 'R_Ring3', 'R_Thumb1', 'R_Thumb2', 'R_Thumb3']

if __name__ == "__main__":
    print(SMPL_BONE_ORDER_NAMES)