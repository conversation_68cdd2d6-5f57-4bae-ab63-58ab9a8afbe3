#!/usr/bin/env python3
"""
Example of using MoE in MDM (Motion Diffusion Model)

This example shows how to:
1. Create an MDM model with MoE architecture
2. Handle the load balancing loss during training
3. Compare performance with standard MDM
"""

import torch
import torch.nn as nn
import numpy as np
from closd.diffusion_planner.model.mdm import MDM


def create_mdm_with_moe(
    use_moe=True,
    num_experts=8,
    top_k=2,
    load_balance_weight=0.01,
    **kwargs
):
    """
    Create MDM model with optional MoE support.
    
    Args:
        use_moe: whether to use MoE architecture
        num_experts: number of experts in each MoE layer
        top_k: number of experts to activate per token
        load_balance_weight: weight for load balancing loss
        **kwargs: other MDM parameters
        
    Returns:
        MDM model
    """
    
    # Default MDM parameters
    default_params = {
        'modeltype': 'mdm',
        'njoints': 22,
        'nfeats': 6,  # 6D rotation representation
        'num_actions': 1,
        'translation': True,
        'pose_rep': 'rot6d',
        'glob': True,
        'glob_rot': True,
        'latent_dim': 512,
        'ff_size': 1024,
        'num_layers': 8,
        'num_heads': 8,
        'dropout': 0.1,
        'activation': 'gelu',
        'arch': 'trans_dec',  # Use transformer decoder
        'cond_mode': 'text',
        'clip_version': 'ViT-B/32',
        'dataset': 'humanml'
    }
    
    # Update with provided parameters
    default_params.update(kwargs)
    
    # Add MoE parameters
    default_params.update({
        'use_moe': use_moe,
        'num_experts': num_experts,
        'top_k': top_k,
        'load_balance_weight': load_balance_weight
    })
    
    return MDM(**default_params)


def test_mdm_moe():
    """Test MDM with and without MoE"""
    
    print("Testing MDM with MoE...")
    
    # Model parameters
    batch_size = 4
    nframes = 60
    njoints = 22
    nfeats = 6
    
    # Create models
    print("\n1. Creating standard MDM...")
    standard_mdm = create_mdm_with_moe(
        use_moe=False,
        latent_dim=256,
        ff_size=512,
        num_layers=4
    )
    
    print("2. Creating MoE MDM...")
    moe_mdm = create_mdm_with_moe(
        use_moe=True,
        num_experts=8,
        top_k=2,
        load_balance_weight=0.01,
        latent_dim=256,
        ff_size=512,
        num_layers=4
    )
    
    # Create sample data
    print(f"\n3. Creating sample data...")
    print(f"   Batch size: {batch_size}")
    print(f"   Sequence length: {nframes}")
    print(f"   Joints: {njoints}, Features: {nfeats}")
    
    # Motion data: [batch_size, njoints, nfeats, nframes]
    x = torch.randn(batch_size, njoints, nfeats, nframes)
    
    # Timesteps for diffusion
    timesteps = torch.randint(0, 1000, (batch_size,))
    
    # Conditioning data
    y = {
        'text': ['a person walks forward'] * batch_size,
        'mask': torch.ones(batch_size, 1, 1, nframes, dtype=torch.bool)
    }
    
    print(f"   Input shape: {x.shape}")
    print(f"   Timesteps shape: {timesteps.shape}")
    
    # Test standard MDM
    print(f"\n4. Testing standard MDM...")
    with torch.no_grad():
        standard_output = standard_mdm(x, timesteps, y)
        print(f"   Standard output shape: {standard_output.shape}")
    
    # Test MoE MDM
    print(f"\n5. Testing MoE MDM...")
    with torch.no_grad():
        moe_output = moe_mdm(x, timesteps, y)
        load_loss = moe_mdm.get_load_balancing_loss()
        print(f"   MoE output shape: {moe_output.shape}")
        print(f"   Load balancing loss: {load_loss:.6f}")
    
    # Parameter comparison
    standard_params = sum(p.numel() for p in standard_mdm.parameters())
    moe_params = sum(p.numel() for p in moe_mdm.parameters())
    
    print(f"\n6. Parameter comparison:")
    print(f"   Standard MDM parameters: {standard_params:,}")
    print(f"   MoE MDM parameters: {moe_params:,}")
    print(f"   Parameter ratio (MoE/Standard): {moe_params/standard_params:.2f}x")
    
    return standard_mdm, moe_mdm


def training_example():
    """Example training loop with MoE MDM"""
    
    print("\n" + "="*60)
    print("Training Example with MoE MDM")
    print("="*60)
    
    # Create MoE MDM
    model = create_mdm_with_moe(
        use_moe=True,
        num_experts=4,
        top_k=2,
        load_balance_weight=0.01,
        latent_dim=256,
        ff_size=512,
        num_layers=4
    )
    
    # Create optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # Sample training data
    batch_size = 2
    nframes = 40
    njoints = 22
    nfeats = 6
    
    x = torch.randn(batch_size, njoints, nfeats, nframes)
    timesteps = torch.randint(0, 1000, (batch_size,))
    y = {
        'text': ['a person dances', 'a person jumps'],
        'mask': torch.ones(batch_size, 1, 1, nframes, dtype=torch.bool)
    }
    
    # Target (for demonstration - in real training this would be noise)
    target = torch.randn_like(x)
    
    print(f"Training data shapes:")
    print(f"  Input: {x.shape}")
    print(f"  Target: {target.shape}")
    print(f"  Timesteps: {timesteps.shape}")
    
    # Training step
    print(f"\nTraining step:")
    
    # Forward pass
    model.train()
    output = model(x, timesteps, y)
    
    # Compute main loss (MSE for demonstration)
    main_loss = nn.MSELoss()(output, target)
    
    # Get total loss including load balancing
    total_loss = model.get_total_loss(main_loss)
    load_loss = model.get_load_balancing_loss()
    
    print(f"  Main loss: {main_loss.item():.6f}")
    print(f"  Load balancing loss: {load_loss:.6f}")
    print(f"  Total loss: {total_loss.item():.6f}")
    print(f"  Load balance weight: {model.load_balance_weight}")
    
    # Backward pass
    optimizer.zero_grad()
    total_loss.backward()
    
    # Gradient clipping (optional)
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
    
    optimizer.step()
    
    print(f"  Training step completed!")
    
    # Evaluation mode
    print(f"\nEvaluation mode:")
    model.eval()
    with torch.no_grad():
        eval_output = model(x, timesteps, y)
        eval_load_loss = model.get_load_balancing_loss()
        print(f"  Eval output shape: {eval_output.shape}")
        print(f"  Eval load loss: {eval_load_loss:.6f}")


def expert_utilization_analysis():
    """Analyze expert utilization in MoE MDM"""
    
    print("\n" + "="*60)
    print("Expert Utilization Analysis")
    print("="*60)
    
    # Create MoE MDM with more experts
    model = create_mdm_with_moe(
        use_moe=True,
        num_experts=8,
        top_k=2,
        load_balance_weight=0.01,
        latent_dim=256,
        ff_size=512,
        num_layers=2  # Fewer layers for faster analysis
    )
    
    # Sample data
    batch_size = 4
    nframes = 30
    njoints = 22
    nfeats = 6
    
    x = torch.randn(batch_size, nframes, njoints, nfeats)
    timesteps = torch.randint(0, 1000, (batch_size,))
    y = {
        'text': ['walking', 'running', 'dancing', 'jumping'],
        'mask': torch.ones(batch_size, 1, 1, nframes, dtype=torch.bool)
    }
    
    print(f"Analyzing expert usage with:")
    print(f"  {model.num_experts} experts, top-{model.top_k} routing")
    print(f"  {model.num_layers} layers")
    print(f"  Batch size: {batch_size}, Sequence length: {nframes}")
    
    # Multiple forward passes to analyze expert usage
    model.eval()
    total_load_loss = 0.0
    num_passes = 10
    
    for i in range(num_passes):
        with torch.no_grad():
            _ = model(x, timesteps, y)
            total_load_loss += model.get_load_balancing_loss()
    
    avg_load_loss = total_load_loss / num_passes
    print(f"\nAverage load balancing loss over {num_passes} passes: {avg_load_loss:.6f}")
    print(f"This indicates expert utilization balance (lower is more balanced)")


if __name__ == "__main__":
    # Run tests
    test_mdm_moe()
    training_example()
    expert_utilization_analysis()
    
    print(f"\n" + "="*60)
    print("All tests completed successfully!")
    print("="*60)
