#!/usr/bin/env python3
"""
Usage example for MoE Transformer Decoder Layer

This example shows how to:
1. Replace standard TransformerDecoderLayer with MoETransformerDecoderLayer
2. Handle the load balancing loss
3. Create a complete MoE-based Transformer Decoder
"""

import torch
import torch.nn as nn
from typing import Optional
from closd.diffusion_planner.model.transformer import (
    TransformerDecoder,
    MoETransformerDecoderLayer,
    LayerNorm,
    _get_clones
)


class MoETransformerDecoder(nn.Module):
    """
    Transformer Decoder with MoE layers.
    
    This is a modified version of TransformerDecoder that uses MoE layers
    and properly handles load balancing loss.
    """
    
    def __init__(
        self,
        decoder_layer: MoETransformerDecoderLayer,
        num_layers: int,
        norm: Optional[nn.Module] = None,
        return_intermediate: bool = False
    ) -> None:
        super().__init__()
        self.layers = _get_clones(decoder_layer, num_layers)
        self.num_layers = num_layers
        self.norm = norm
        self.return_intermediate = return_intermediate
        
    def forward(
        self, 
        tgt: torch.Tensor, 
        memory: torch.Tensor, 
        tgt_mask: Optional[torch.Tensor] = None,
        memory_mask: Optional[torch.Tensor] = None, 
        tgt_key_padding_mask: Optional[torch.Tensor] = None,
        memory_key_padding_mask: Optional[torch.Tensor] = None, 
        tgt_is_causal: Optional[bool] = None,
        memory_is_causal: bool = False
    ) -> tuple:
        """
        Returns:
            output: decoder output
            total_load_loss: accumulated load balancing loss from all layers
            intermediate: intermediate outputs (if return_intermediate=True)
        """
        output = tgt
        intermediate = []
        total_load_loss = 0.0
        
        for mod in self.layers:
            output, load_loss = mod(
                output, memory,
                tgt_mask=tgt_mask,
                memory_mask=memory_mask,
                tgt_key_padding_mask=tgt_key_padding_mask,
                memory_key_padding_mask=memory_key_padding_mask,
                tgt_is_causal=tgt_is_causal if tgt_is_causal is not None else False,
                memory_is_causal=memory_is_causal
            )
            total_load_loss += load_loss
            
            if self.return_intermediate:
                intermediate.append(output)

        if self.norm is not None:
            output = self.norm(output)

        if self.return_intermediate:
            return output, total_load_loss, intermediate
        else:
            return output, total_load_loss


def create_moe_transformer_decoder(
    d_model: int = 512,
    nhead: int = 8,
    num_layers: int = 6,
    num_experts: int = 4,
    top_k: int = 2,
    dim_feedforward: int = 2048,
    dropout: float = 0.1,
    layer_norm_eps: float = 1e-5,
    batch_first: bool = True,
    norm_first: bool = False,
    bias: bool = True,
    device=None,
    dtype=None
) -> MoETransformerDecoder:
    """
    Create a MoE-based Transformer Decoder.
    
    Args:
        d_model: model dimension
        nhead: number of attention heads
        num_layers: number of decoder layers
        num_experts: number of experts in each MoE layer
        top_k: number of experts to route to for each token
        dim_feedforward: feedforward dimension
        dropout: dropout rate
        layer_norm_eps: layer norm epsilon
        batch_first: whether batch dimension comes first
        norm_first: whether to use pre-norm or post-norm
        bias: whether to use bias in linear layers
        device: device to create tensors on
        dtype: data type for tensors
        
    Returns:
        MoE Transformer Decoder
    """
    factory_kwargs = {'device': device, 'dtype': dtype}
    
    # Create MoE decoder layer
    decoder_layer = MoETransformerDecoderLayer(
        d_model=d_model,
        nhead=nhead,
        num_experts=num_experts,
        top_k=top_k,
        dim_feedforward=dim_feedforward,
        dropout=dropout,
        layer_norm_eps=layer_norm_eps,
        batch_first=batch_first,
        norm_first=norm_first,
        bias=bias,
        **factory_kwargs
    )
    
    # Create layer norm
    decoder_norm = LayerNorm(d_model, eps=layer_norm_eps, bias=bias, **factory_kwargs)
    
    # Create MoE decoder
    decoder = MoETransformerDecoder(decoder_layer, num_layers, decoder_norm)
    
    return decoder


def example_usage():
    """Example of how to use MoE Transformer Decoder"""
    
    print("Creating MoE Transformer Decoder...")
    
    # Model parameters
    d_model = 512
    nhead = 8
    num_layers = 6
    num_experts = 8
    top_k = 2
    batch_size = 4
    seq_len = 20
    src_len = 30
    
    # Create MoE decoder
    moe_decoder = create_moe_transformer_decoder(
        d_model=d_model,
        nhead=nhead,
        num_layers=num_layers,
        num_experts=num_experts,
        top_k=top_k,
        batch_first=True
    )
    
    print(f"Created MoE decoder with {num_layers} layers, {num_experts} experts per layer")
    
    # Create sample data
    tgt = torch.randn(batch_size, seq_len, d_model)
    memory = torch.randn(batch_size, src_len, d_model)
    
    print(f"Input shapes: tgt={tgt.shape}, memory={memory.shape}")
    
    # Forward pass
    output, total_load_loss = moe_decoder(tgt, memory)
    
    print(f"Output shape: {output.shape}")
    print(f"Total load balancing loss: {total_load_loss:.6f}")
    print(f"Average load loss per layer: {total_load_loss/num_layers:.6f}")
    
    # Example training loop with load balancing
    print("\nExample training step with load balancing:")
    
    # Create optimizer
    optimizer = torch.optim.Adam(moe_decoder.parameters(), lr=1e-4)
    
    # Forward pass
    output, load_loss = moe_decoder(tgt, memory)
    
    # Compute main task loss (example: MSE with target)
    target = torch.randn_like(output)
    main_loss = nn.MSELoss()(output, target)
    
    # Combine losses (load balancing weight is typically small)
    load_balance_weight = 0.01
    total_loss = main_loss + load_balance_weight * load_loss
    
    print(f"Main loss: {main_loss:.6f}")
    print(f"Load balancing loss: {load_loss:.6f}")
    print(f"Total loss: {total_loss:.6f}")
    
    # Backward pass
    optimizer.zero_grad()
    total_loss.backward()
    optimizer.step()
    
    print("Training step completed!")
    
    # Parameter count comparison
    total_params = sum(p.numel() for p in moe_decoder.parameters())
    print(f"\nTotal parameters: {total_params:,}")
    
    # Estimate equivalent standard transformer size
    standard_ff_params_per_layer = d_model * 2048 * 2  # linear1 + linear2
    moe_ff_params_per_layer = d_model * 2048 * 2 * num_experts  # experts
    standard_equivalent_layers = (total_params * num_layers) / (
        total_params - (moe_ff_params_per_layer - standard_ff_params_per_layer) * num_layers
    )
    
    print(f"Equivalent standard transformer layers: {standard_equivalent_layers:.1f}")
    print(f"Parameter efficiency: {standard_equivalent_layers/num_layers:.2f}x")


if __name__ == "__main__":
    example_usage()
