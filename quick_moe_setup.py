#!/usr/bin/env python3
"""
Quick setup guide for enabling MoE in MDM

This script shows the minimal changes needed to enable MoE in existing MDM code.
"""

import torch
import torch.nn as nn
from closd.diffusion_planner.model.mdm import MDM


def create_standard_mdm():
    """Create a standard MDM model"""
    return MDM(
        modeltype='mdm',
        njoints=22,
        nfeats=6,
        num_actions=1,
        translation=True,
        pose_rep='rot6d',
        glob=True,
        glob_rot=True,
        latent_dim=512,
        ff_size=1024,
        num_layers=8,
        num_heads=8,
        dropout=0.1,
        activation='gelu',
        arch='trans_dec',
        cond_mode='text',
        clip_version='ViT-B/32',
        dataset='humanml'
    )


def create_moe_mdm():
    """Create an MDM model with MoE - just add 4 parameters!"""
    return MDM(
        modeltype='mdm',
        njoints=22,
        nfeats=6,
        num_actions=1,
        translation=True,
        pose_rep='rot6d',
        glob=True,
        glob_rot=True,
        latent_dim=512,
        ff_size=1024,
        num_layers=8,
        num_heads=8,
        dropout=0.1,
        activation='gelu',
        arch='trans_dec',
        cond_mode='text',
        clip_version='ViT-B/32',
        dataset='humanml',
        
        # 🚀 MoE Configuration - Only 4 lines to add!
        use_moe=True,              # Enable MoE
        num_experts=8,             # Number of experts
        top_k=2,                   # Experts per token
        load_balance_weight=0.01   # Load balancing weight
    )


def standard_training_loop(model, x, timesteps, y, target, optimizer):
    """Standard training loop"""
    # Forward pass
    output = model(x, timesteps, y)
    
    # Compute loss
    loss = nn.MSELoss()(output, target)
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    return loss


def moe_training_loop(model, x, timesteps, y, target, optimizer):
    """MoE training loop - only 2 lines changed!"""
    # Forward pass
    output = model(x, timesteps, y)
    
    # Compute loss - 🔥 This is the key change!
    main_loss = nn.MSELoss()(output, target)
    total_loss = model.get_total_loss(main_loss)  # ← Add this line
    
    # Backward pass
    optimizer.zero_grad()
    total_loss.backward()  # ← Use total_loss instead of main_loss
    optimizer.step()
    
    # Optional: Monitor load balancing
    load_loss = model.get_load_balancing_loss()
    print(f"Main: {main_loss:.4f}, Load: {load_loss:.4f}, Total: {total_loss:.4f}")
    
    return total_loss


def quick_comparison():
    """Quick comparison between standard and MoE MDM"""
    
    print("🔥 Quick MoE Setup Comparison")
    print("=" * 50)
    
    # Create models
    print("Creating models...")
    standard_model = create_standard_mdm()
    moe_model = create_moe_mdm()
    
    # Sample data
    batch_size = 2
    nframes = 30
    x = torch.randn(batch_size, 22, 6, nframes)
    timesteps = torch.randint(0, 1000, (batch_size,))
    y = {
        'text': ['walking', 'running'],
        'mask': torch.ones(batch_size, 1, 1, nframes, dtype=torch.bool)
    }
    target = torch.randn_like(x)
    
    # Create optimizers
    standard_optimizer = torch.optim.AdamW(standard_model.parameters(), lr=1e-4)
    moe_optimizer = torch.optim.AdamW(moe_model.parameters(), lr=1e-4)
    
    print(f"\nInput shape: {x.shape}")
    
    # Test standard training
    print(f"\n📊 Standard MDM:")
    standard_model.train()
    standard_loss = standard_training_loop(
        standard_model, x, timesteps, y, target, standard_optimizer
    )
    print(f"Loss: {standard_loss:.6f}")
    
    # Test MoE training
    print(f"\n🚀 MoE MDM:")
    moe_model.train()
    moe_loss = moe_training_loop(
        moe_model, x, timesteps, y, target, moe_optimizer
    )
    
    # Parameter comparison
    standard_params = sum(p.numel() for p in standard_model.parameters())
    moe_params = sum(p.numel() for p in moe_model.parameters())
    
    print(f"\n📈 Parameter Comparison:")
    print(f"Standard: {standard_params:,}")
    print(f"MoE:      {moe_params:,}")
    print(f"Ratio:    {moe_params/standard_params:.2f}x")
    
    print(f"\n✅ Setup complete! MoE is working.")


def migration_checklist():
    """Checklist for migrating existing MDM code to MoE"""
    
    print("\n" + "🔧 Migration Checklist")
    print("=" * 50)
    
    checklist = [
        "✅ 1. Add MoE parameters to MDM initialization",
        "   - use_moe=True",
        "   - num_experts=8",
        "   - top_k=2", 
        "   - load_balance_weight=0.01",
        "",
        "✅ 2. Update training loop",
        "   - Replace: loss = criterion(output, target)",
        "   - With:    main_loss = criterion(output, target)",
        "   -          total_loss = model.get_total_loss(main_loss)",
        "",
        "✅ 3. Use total_loss for backward pass",
        "   - total_loss.backward()",
        "",
        "✅ 4. Optional: Monitor load balancing",
        "   - load_loss = model.get_load_balancing_loss()",
        "",
        "✅ 5. Ensure arch='trans_dec'",
        "   - MoE only works with transformer decoder",
        "",
        "⚠️  6. Considerations:",
        "   - Slightly higher memory usage",
        "   - Monitor expert utilization",
        "   - Tune load_balance_weight if needed"
    ]
    
    for item in checklist:
        print(item)


def hyperparameter_guide():
    """Quick hyperparameter selection guide"""
    
    print("\n" + "⚙️ Hyperparameter Quick Guide")
    print("=" * 50)
    
    scenarios = {
        "🧪 Experimentation": {
            "num_experts": 4,
            "top_k": 2,
            "load_balance_weight": 0.01,
            "description": "Good for initial testing and small datasets"
        },
        "🎯 Production": {
            "num_experts": 8,
            "top_k": 2,
            "load_balance_weight": 0.02,
            "description": "Balanced performance and efficiency"
        },
        "🚀 High Performance": {
            "num_experts": 16,
            "top_k": 2,
            "load_balance_weight": 0.05,
            "description": "Maximum model capacity for large datasets"
        },
        "⚡ Fast Inference": {
            "num_experts": 8,
            "top_k": 1,
            "load_balance_weight": 0.01,
            "description": "Reduced computation for faster inference"
        }
    }
    
    for scenario, config in scenarios.items():
        print(f"\n{scenario}")
        print(f"  num_experts: {config['num_experts']}")
        print(f"  top_k: {config['top_k']}")
        print(f"  load_balance_weight: {config['load_balance_weight']}")
        print(f"  → {config['description']}")


if __name__ == "__main__":
    # Run quick comparison
    quick_comparison()
    
    # Show migration checklist
    migration_checklist()
    
    # Show hyperparameter guide
    hyperparameter_guide()
    
    print(f"\n🎉 Ready to use MoE in your MDM project!")
    print(f"💡 Tip: Start with the 'Experimentation' settings and scale up as needed.")
