absl-py==2.1.0
addict==2.4.0
aiohttp==3.9.5
aiosignal==1.3.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
autograd==1.6.2
backcall==0.2.0
beautifulsoup4==4.12.3
blinker==1.8.2
blis==0.7.11
blobfile==3.0.0
cachetools==5.3.3
catalogue==2.0.10
cchardet==2.1.7
chardet==5.2.0
chumpy==0.70
click==8.1.7
clip @ git+https://github.com/openai/CLIP.git@dcba3cb2e2827b402d2701e7e1c7d9fed8a20ef1
cloudpathlib==0.18.1
cloudpickle==3.0.0
comm==0.2.2
confection==0.1.5
ConfigArgParse==1.7
configer==1.3.1
configparser==7.0.0
contourpy==1.1.1
cycler==0.12.1
cymem==2.0.8
dash==2.17.1
dash-core-components==2.0.0
dash-html-components==2.0.0
dash-table==5.0.0
decorator==4.4.2
docker-pycreds==0.4.0
easydict==1.13
etils==1.3.0
executing==2.0.1
fastjsonschema==2.20.0
Flask==3.0.3
fonttools==4.53.1
freetype-py==2.4.0
frozenlist==1.4.1
fsspec==2024.6.1
ftfy==6.2.0
future==1.0.0
fvcore==0.1.5.post20221221
gdown==5.2.0
gitdb==4.0.11
GitPython==3.1.43
glfw==2.7.0
google-auth==2.32.0
google-auth-oauthlib==1.0.0
grpcio==1.65.0
gym==0.26.2
gym-notices==0.0.8
huggingface-hub==0.23.4
human_body_prior==*******
Hydra==2.5
hydra-core==1.3.2
imageio==2.34.2
imageio-ffmpeg==0.5.1
importlib_metadata==8.0.0
importlib_resources==6.4.0
iopath==0.1.10
ipdb==0.13.13
ipython==8.12.3
ipywidgets==8.1.3
itsdangerous==2.2.0
jedi==0.19.1
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter_core==5.7.2
jupyterlab_widgets==3.0.11
kiwisolver==1.4.5
langcodes==3.4.0
language_data==1.2.0
lazy_loader==0.4
lightning-utilities==0.11.3.post0
lxml==5.2.2
marisa-trie==1.2.0
Markdown==3.6
markdown-it-py==3.0.0
matplotlib==3.4.3
matplotlib-inline==0.1.7
mdurl==0.1.2
mkl-service==2.4.0
moviepy==1.0.3
msgpack==1.0.8
mujoco==3.1.6
multidict==6.0.5
murmurhash==1.0.10
nbformat==5.10.4
nest-asyncio==1.6.0
ninja==********
numpy==1.21.1
numpy-stl==3.1.1
oauthlib==3.2.2
omegaconf==2.3.0
open3d==0.18.0
opencv-python==********
packaging==24.1
pandas==2.0.3
parso==0.8.4
patchelf==********
pexpect==4.9.0
pickleshare==0.7.5
pkgutil_resolve_name==1.3.10
platformdirs==4.2.2
plotly==5.22.0
portalocker==2.10.0
preshed==3.0.9
proglog==0.1.10
prompt_toolkit==3.0.47
protobuf==5.27.2
psutil==6.0.0
ptyprocess==0.7.0
pure-eval==0.2.2
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycryptodomex==3.21.0
pydantic==1.7.4
pydantic_core==2.20.1
pyglet==2.0.15
Pygments==2.18.0
PyOpenGL==3.1.0
pyparsing==3.1.2
pyquaternion==0.9.9
pyrender==0.1.45
python-dateutil==2.9.0.post0
python-utils==3.8.2
pytorch-lightning==2.3.3
pytorch3d==0.3.0
pytz==2024.1
PyVirtualDisplay==3.0
PyWavelets==1.4.1
PyYAML==6.0.1
ray==2.10.0
referencing==0.35.1
regex==2024.5.15
requests-oauthlib==2.0.0
retrying==1.3.4
rich==13.7.1
rl-games==1.1.4
rpds-py==0.19.0
rsa==4.9
safetensors==0.4.3
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.10.1
sentry-sdk==2.9.0
setproctitle==1.3.3
shellingham==1.5.4
six==1.16.0
smart-open==7.0.4
smmap==5.0.1
smplx @ git+https://github.com/ZhengyiLuo/smplx.git@a5b8e4ac14f79f3f33fd2cf2a16e6f507146b813
soupsieve==2.5
spacy==3.7.5
spacy-legacy==3.0.12
spacy-loggers==1.0.5
srsly==2.4.8
stack-data==0.6.3
tabulate==0.9.0
tenacity==8.5.0
tensorboard==2.14.0
tensorboard-data-server==0.7.2
tensorboardX==*******
termcolor==2.4.0
thinc==8.2.3
threadpoolctl==3.5.0
tifffile==2023.7.10
tokenizers==0.15.2
tomli==2.0.1
torch
torchaudio
torchgeometry
torchmetrics
torchvision
tqdm==4.66.4
traitlets==5.14.3
transformers==4.38.2
transforms3d==0.4.2
trimesh==4.4.3
triton==2.2.0
typer==0.12.3
tzdata==2024.1
vtk==9.3.1
wandb==0.17.4
wasabi==0.10.1
wcwidth==0.2.13
weasel==0.4.1
Werkzeug==3.0.3
widgetsnbextension==4.0.11
wrapt==1.16.0
yacs==0.1.8
yarl==1.9.4
zipp==3.19.2