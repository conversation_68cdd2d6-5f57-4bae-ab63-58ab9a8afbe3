#!/usr/bin/env python3
"""
Test script for MoE Transformer Decoder Layer
"""

import torch
import torch.nn as nn
from closd.diffusion_planner.model.transformer import (
    TransformerDecoderLayer, 
    MoETransformerDecoderLayer
)


def test_moe_transformer_layer():
    """Test the MoE Transformer Decoder Layer"""
    
    # Model parameters
    d_model = 512
    nhead = 8
    num_experts = 4
    top_k = 2
    dim_feedforward = 2048
    batch_size = 2
    seq_len = 10
    src_len = 15
    
    print(f"Testing MoE Transformer Decoder Layer")
    print(f"d_model: {d_model}, nhead: {nhead}, num_experts: {num_experts}, top_k: {top_k}")
    print(f"batch_size: {batch_size}, seq_len: {seq_len}, src_len: {src_len}")
    
    # Create models
    standard_layer = TransformerDecoderLayer(
        d_model=d_model,
        nhead=nhead,
        dim_feedforward=dim_feedforward,
        batch_first=True
    )
    
    moe_layer = MoETransformerDecoderLayer(
        d_model=d_model,
        nhead=nhead,
        num_experts=num_experts,
        top_k=top_k,
        dim_feedforward=dim_feedforward,
        batch_first=True
    )
    
    # Create test data
    tgt = torch.randn(batch_size, seq_len, d_model)
    memory = torch.randn(batch_size, src_len, d_model)
    
    print(f"\nInput shapes:")
    print(f"tgt: {tgt.shape}")
    print(f"memory: {memory.shape}")
    
    # Test standard layer
    with torch.no_grad():
        standard_output = standard_layer(tgt, memory)
        print(f"\nStandard layer output shape: {standard_output.shape}")
    
    # Test MoE layer
    with torch.no_grad():
        moe_output, load_loss = moe_layer(tgt, memory)
        print(f"MoE layer output shape: {moe_output.shape}")
        print(f"Load balancing loss: {load_loss.item():.6f}")
    
    # Test with gradients
    print(f"\nTesting with gradients...")
    
    # Standard layer
    tgt_grad = tgt.clone().requires_grad_(True)
    memory_grad = memory.clone().requires_grad_(True)
    standard_output = standard_layer(tgt_grad, memory_grad)
    loss = standard_output.sum()
    loss.backward()
    print(f"Standard layer - Loss: {loss.item():.6f}")
    
    # MoE layer
    tgt_grad = tgt.clone().requires_grad_(True)
    memory_grad = memory.clone().requires_grad_(True)
    moe_output, load_loss = moe_layer(tgt_grad, memory_grad)
    total_loss = moe_output.sum() + 0.01 * load_loss  # Add load balancing loss
    total_loss.backward()
    print(f"MoE layer - Output loss: {moe_output.sum().item():.6f}, Load loss: {load_loss.item():.6f}, Total: {total_loss.item():.6f}")
    
    # Compare parameter counts
    standard_params = sum(p.numel() for p in standard_layer.parameters())
    moe_params = sum(p.numel() for p in moe_layer.parameters())
    
    print(f"\nParameter comparison:")
    print(f"Standard layer parameters: {standard_params:,}")
    print(f"MoE layer parameters: {moe_params:,}")
    print(f"Parameter ratio (MoE/Standard): {moe_params/standard_params:.2f}x")
    
    print(f"\nTest completed successfully!")


def test_expert_utilization():
    """Test expert utilization in MoE layer"""
    
    print(f"\n" + "="*50)
    print(f"Testing Expert Utilization")
    print(f"="*50)
    
    d_model = 256
    nhead = 4
    num_experts = 8
    top_k = 2
    batch_size = 4
    seq_len = 20
    src_len = 25
    
    moe_layer = MoETransformerDecoderLayer(
        d_model=d_model,
        nhead=nhead,
        num_experts=num_experts,
        top_k=top_k,
        batch_first=True
    )
    
    # Create test data
    tgt = torch.randn(batch_size, seq_len, d_model)
    memory = torch.randn(batch_size, src_len, d_model)
    
    # Test multiple forward passes to see expert usage
    expert_usage = torch.zeros(num_experts)
    num_tests = 10
    
    for i in range(num_tests):
        with torch.no_grad():
            # Get gate decisions
            gates, indices, _ = moe_layer.moe.gate(tgt)
            
            # Count expert usage
            for expert_idx in range(num_experts):
                expert_usage[expert_idx] += (indices == expert_idx).sum().item()
    
    expert_usage = expert_usage / expert_usage.sum()
    
    print(f"Expert utilization over {num_tests} forward passes:")
    for i, usage in enumerate(expert_usage):
        print(f"Expert {i}: {usage:.3f} ({usage*100:.1f}%)")
    
    # Calculate utilization variance (lower is better for load balancing)
    utilization_var = torch.var(expert_usage).item()
    print(f"\nUtilization variance: {utilization_var:.6f}")
    print(f"Ideal variance (uniform): {1.0/(num_experts**2):.6f}")


if __name__ == "__main__":
    test_moe_transformer_layer()
    test_expert_utilization()
