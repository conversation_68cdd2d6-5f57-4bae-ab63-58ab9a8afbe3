# export ALL_PROXY="http://127.0.0.1:7890"
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=/mnt/data/zawang/huggingface
export WANDB_BASE_URL=https://api.bandw.top

export WANDB_API_KEY=****************************************
export WANDB_MODE=offline
export HF_HOME=/mnt/data/zawang/huggingface
CUDA_VISIBLE_DEVICES=3 python -m closd.diffusion_planner.train.train_mdm\
 --save_dir closd/diffusion_planner/save/my_DiP_\
 --dataset humanml --arch trans_dec --text_encoder_type bert\
 --diffusion_steps 10 --context_len 20 --pred_len 40 --lambda_target_loc 0 \
 --mask_frames --eval_during_training --gen_during_training --overwrite --use_ema --autoregressive --train_platform_type WandBPlatform